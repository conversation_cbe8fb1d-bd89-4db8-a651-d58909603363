import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart'; // Temporarily removed
import 'package:dio/dio.dart';

import '../../data/datasources/local/preferences_data_source.dart';
import '../../data/datasources/remote/api_client.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/splash_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/splash_repository.dart';
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/get_user_profile_usecase.dart';
import '../../domain/usecases/settings/get_app_config_usecase.dart';

final GetIt sl = GetIt.instance;

Future<void> initServiceLocator() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  // Temporarily removed FlutterSecureStorage
  // sl.registerLazySingleton<FlutterSecureStorage>(
  //   () => const FlutterSecureStorage(),
  // );

  sl.registerLazySingleton<Dio>(() {
    final dio = Dio();
    // Configure Dio here (base URL, interceptors, etc.)
    return dio;
  });

  // Data sources
  sl.registerLazySingleton<PreferencesDataSource>(
    () => PreferencesDataSourceImpl(sl()),
  );

  sl.registerLazySingleton<ApiClient>(
    () => ApiClientImpl(sl()),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(sl(), sl()),
  );

  sl.registerLazySingleton<SplashRepository>(
    () => SplashRepositoryImpl(sl(), sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => GetUserProfileUseCase(sl()));
  sl.registerLazySingleton(() => GetAppConfigUseCase(sl()));

  // BLoCs will be registered later
}
