class LanguageModel {
  String? imageUrl;
  String? languageName;
  String? languageCode;
  String? countryCode;

  LanguageModel({
    this.imageUrl,
    this.languageName,
    this.countryCode,
    this.languageCode,
  });

  LanguageModel.fromJson(Map<String, dynamic> json) {
    imageUrl = json['image_url'];
    languageName = json['language_name'];
    languageCode = json['language_code'];
    countryCode = json['country_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['image_url'] = imageUrl;
    data['language_name'] = languageName;
    data['language_code'] = languageCode;
    data['country_code'] = countryCode;
    return data;
  }
}
