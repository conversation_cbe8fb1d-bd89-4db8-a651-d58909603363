import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../domain/usecases/auth/login_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/get_user_profile_usecase.dart';
import '../../domain/repositories/auth_repository.dart';
import 'service_locator.dart';

class BlocProviders {
  static List<BlocProvider> get providers => [
    BlocProvider<AuthBloc>(
      create: (context) => AuthBloc(
        loginUseCase: sl<LoginUseCase>(),
        logoutUseCase: sl<LogoutUseCase>(),
        getUserProfileUseCase: sl<GetUserProfileUseCase>(),
        authRepository: sl<AuthRepository>(),
      ),
    ),
    // Ajoutez d'autres BLoCs ici
  ];
}
