// Base exception class
abstract class AppException implements Exception {
  final String message;
  
  AppException({required this.message});
  
  @override
  String toString() => message;
}

// Network related exceptions
class NetworkException extends AppException {
  NetworkException({required super.message});
}

class ServerException extends AppException {
  final int statusCode;
  
  ServerException({required this.statusCode, required super.message});
  
  @override
  String toString() => 'ServerException: $statusCode - $message';
}

class TimeoutException extends AppException {
  TimeoutException({required super.message});
}

class RequestCancelledException extends AppException {
  RequestCancelledException({required super.message});
}

// Authentication related exceptions
class AuthenticationException extends AppException {
  AuthenticationException({required super.message});
}

class UnauthorizedException extends AppException {
  UnauthorizedException({required super.message});
}

// Data related exceptions
class CacheException extends AppException {
  CacheException({required super.message});
}

class DataParsingException extends AppException {
  DataParsingException({required super.message});
}

// Permission related exceptions
class PermissionDeniedException extends AppException {
  PermissionDeniedException({required super.message});
}

// Generic exceptions
class UnknownException extends AppException {
  UnknownException({required super.message});
}
