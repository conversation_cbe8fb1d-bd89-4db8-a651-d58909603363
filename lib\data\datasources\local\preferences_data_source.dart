import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';

abstract class PreferencesDataSource {
  Future<bool> saveToken(String token);
  String? getToken();
  Future<bool> clearToken();
  Future<bool> saveTheme(String theme);
  String? getTheme();
  Future<bool> saveLanguageCode(String languageCode);
  String? getLanguageCode();
}

class PreferencesDataSourceImpl implements PreferencesDataSource {
  final SharedPreferences sharedPreferences;

  PreferencesDataSourceImpl(this.sharedPreferences);

  @override
  Future<bool> saveToken(String token) async {
    try {
      return await sharedPreferences.setString(AppConstants.token, token);
    } catch (e) {
      throw CacheException(message: 'Failed to save token: $e');
    }
  }

  @override
  String? getToken() {
    try {
      return sharedPreferences.getString(AppConstants.token);
    } catch (e) {
      throw CacheException(message: 'Failed to get token: $e');
    }
  }

  @override
  Future<bool> clearToken() async {
    try {
      return await sharedPreferences.remove(AppConstants.token);
    } catch (e) {
      throw CacheException(message: 'Failed to clear token: $e');
    }
  }

  @override
  Future<bool> saveTheme(String theme) async {
    try {
      return await sharedPreferences.setString(AppConstants.theme, theme);
    } catch (e) {
      throw CacheException(message: 'Failed to save theme: $e');
    }
  }

  @override
  String? getTheme() {
    try {
      return sharedPreferences.getString(AppConstants.theme);
    } catch (e) {
      throw CacheException(message: 'Failed to get theme: $e');
    }
  }

  @override
  Future<bool> saveLanguageCode(String languageCode) async {
    try {
      return await sharedPreferences.setString(AppConstants.languageCode, languageCode);
    } catch (e) {
      throw CacheException(message: 'Failed to save language code: $e');
    }
  }

  @override
  String? getLanguageCode() {
    try {
      return sharedPreferences.getString(AppConstants.languageCode);
    } catch (e) {
      throw CacheException(message: 'Failed to get language code: $e');
    }
  }
}
