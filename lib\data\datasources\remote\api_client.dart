import 'package:dio/dio.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/error/exceptions.dart';

abstract class ApiClient {
  Future<Response> getData(String uri, {Map<String, dynamic>? queryParameters});
  Future<Response> postData(String uri, dynamic data);
  Future<Response> putData(String uri, dynamic data);
  Future<Response> deleteData(String uri);
  void updateHeader(String token);
}

class ApiClientImpl implements ApiClient {
  final Dio dio;

  ApiClientImpl(this.dio) {
    dio.options.baseUrl = ApiConstants.baseUrl;
    dio.options.connectTimeout = Duration(milliseconds: ApiConstants.connectTimeout);
    dio.options.receiveTimeout = Duration(milliseconds: ApiConstants.receiveTimeout);
    dio.options.headers = {
      ApiConstants.contentType: ApiConstants.applicationJson,
    };
  }

  @override
  void updateHeader(String token) {
    dio.options.headers = {
      ApiConstants.contentType: ApiConstants.applicationJson,
      ApiConstants.authorization: '${ApiConstants.bearer} $token',
    };
  }

  @override
  Future<Response> getData(String uri, {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await dio.get(uri, queryParameters: queryParameters);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<Response> postData(String uri, dynamic data) async {
    try {
      final response = await dio.post(uri, data: data);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<Response> putData(String uri, dynamic data) async {
    try {
      final response = await dio.put(uri, data: data);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  @override
  Future<Response> deleteData(String uri) async {
    try {
      final response = await dio.delete(uri);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Exception _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException(message: 'Connection timeout');
      case DioExceptionType.badResponse:
        return ServerException(
          statusCode: error.response?.statusCode ?? 0,
          message: error.response?.statusMessage ?? 'Server error',
        );
      case DioExceptionType.cancel:
        return RequestCancelledException(message: 'Request cancelled');
      case DioExceptionType.connectionError:
        return NetworkException(message: 'No internet connection');
      default:
        return UnknownException(message: error.message ?? 'Unknown error');
    }
  }
}
