name: dyno_mobile_app
description: "Dyno Mobile Application"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.7.2 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1

  # State Management & DI
  get_it: ^7.6.7
  flutter_bloc: ^8.1.4
  equatable: ^2.0.5

  # Authentication and Security
  # flutter_secure_storage: ^9.2.4  # Temporarily removed due to Gradle compatibility issues

  # Device Features
  permission_handler: ^12.0.0+1
  # device_info_plus: ^9.1.2  # Temporarily removed due to Gradle compatibility issues

  # Networking and Storage
  shared_preferences: ^2.2.2
  http: ^0.13.6
  dio: ^5.4.1

  # Utilities
  url_launcher: ^6.2.5
  flutter_local_notifications: ^16.3.2
  path_provider: ^2.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/image/
    - assets/language/
    - assets/animationFile/
    - assets/payment/

  fonts:
    - family: Rubik
      fonts:
        - asset: assets/font/Rubik-Light.ttf
          weight: 300
        - asset: assets/font/Rubik-Regular.ttf
          weight: 400
        - asset: assets/font/Rubik-Medium.ttf
          weight: 500
        - asset: assets/font/Rubik-SemiBold.ttf
          weight: 600
