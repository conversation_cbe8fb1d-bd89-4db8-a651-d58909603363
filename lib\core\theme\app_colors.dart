import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF0157FF);
  static const Color primaryLight = Color(0xFF4B83FF);
  static const Color primaryDark = Color(0xFF0040B5);
  
  // Secondary colors
  static const Color secondary = Color(0xFF00C569);
  static const Color secondaryLight = Color(0xFF4CD98A);
  static const Color secondaryDark = Color(0xFF00913F);
  
  // Neutral colors
  static const Color black = Color(0xFF000000);
  static const Color darkGrey = Color(0xFF4A4A4A);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color lightGrey = Color(0xFFE0E0E0);
  static const Color white = Color(0xFFFFFFFF);
  
  // Semantic colors
  static const Color error = Color(0xFFD32F2F);
  static const Color success = Color(0xFF388E3C);
  static const Color warning = Color(0xFFF57C00);
  static const Color info = Color(0xFF1976D2);
  
  // Background colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color scaffoldBackground = Color(0xFFF5F5F5);
  
  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkCardBackground = Color(0xFF1E1E1E);
  static const Color darkScaffoldBackground = Color(0xFF121212);
}
