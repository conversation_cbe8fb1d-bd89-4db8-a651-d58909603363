import '../../domain/entities/user.dart';

class UserModel {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? image;
  final String? type;
  final String? token;
  final String? fName;
  final String? lName;
  final String? gender;
  final String? occupation;
  final String? dateOfBirth;
  final int? kycStatus;
  final bool? twoFactorStatus;

  UserModel({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.image,
    this.type,
    this.token,
    this.fName,
    this.lName,
    this.gender,
    this.occupation,
    this.dateOfBirth,
    this.kycStatus,
    this.twoFactorStatus,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      image: json['image'],
      type: json['type'],
      token: json['token'],
      fName: json['f_name'],
      lName: json['l_name'],
      gender: json['gender'],
      occupation: json['occupation'],
      dateOfBirth: json['date_of_birth'],
      kycStatus: json['kyc_status'],
      twoFactorStatus: json['two_factor_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'image': image,
      'type': type,
      'token': token,
      'f_name': fName,
      'l_name': lName,
      'gender': gender,
      'occupation': occupation,
      'date_of_birth': dateOfBirth,
      'kyc_status': kycStatus,
      'two_factor_status': twoFactorStatus,
    };
  }

  User toEntity() {
    return User(
      id: id ?? 0,
      name: name ?? '',
      email: email ?? '',
      phone: phone ?? '',
      image: image,
      type: type,
      token: token,
    );
  }
}
