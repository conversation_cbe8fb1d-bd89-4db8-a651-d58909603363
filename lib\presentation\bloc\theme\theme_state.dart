import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class ThemeState extends Equatable {
  const ThemeState();

  @override
  List<Object?> get props => [];
}

class ThemeInitial extends ThemeState {}

class ThemeLoaded extends ThemeState {
  final ThemeMode themeMode;
  final bool isDarkMode;

  const ThemeLoaded({
    required this.themeMode,
    required this.isDarkMode,
  });

  @override
  List<Object?> get props => [themeMode, isDarkMode];
}
