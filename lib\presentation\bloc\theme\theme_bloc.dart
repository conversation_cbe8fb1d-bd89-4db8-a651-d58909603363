import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/constants/app_constants.dart';
import 'theme_event.dart';
import 'theme_state.dart';

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final SharedPreferences sharedPreferences;

  ThemeBloc({required this.sharedPreferences}) : super(ThemeInitial()) {
    on<ToggleThemeEvent>(_onToggleTheme);
    on<SetThemeEvent>(_onSetTheme);
    on<LoadThemeEvent>(_onLoadTheme);
  }

  Future<void> _onToggleTheme(
    ToggleThemeEvent event,
    Emitter<ThemeState> emit,
  ) async {
    if (state is ThemeLoaded) {
      final currentState = state as ThemeLoaded;
      final newIsDarkMode = !currentState.isDarkMode;
      
      await sharedPreferences.setBool(AppConstants.theme, newIsDarkMode);
      
      emit(ThemeLoaded(
        themeMode: newIsDarkMode ? ThemeMode.dark : ThemeMode.light,
        isDarkMode: newIsDarkMode,
      ));
    }
  }

  Future<void> _onSetTheme(
    SetThemeEvent event,
    Emitter<ThemeState> emit,
  ) async {
    await sharedPreferences.setBool(AppConstants.theme, event.isDarkMode);
    
    emit(ThemeLoaded(
      themeMode: event.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      isDarkMode: event.isDarkMode,
    ));
  }

  Future<void> _onLoadTheme(
    LoadThemeEvent event,
    Emitter<ThemeState> emit,
  ) async {
    final isDarkMode = sharedPreferences.getBool(AppConstants.theme) ?? false;
    
    emit(ThemeLoaded(
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      isDarkMode: isDarkMode,
    ));
  }
}
