// import 'package:flutter_secure_storage/flutter_secure_storage.dart'; // Temporarily removed
import 'package:shared_preferences/shared_preferences.dart'; // Temporary replacement
import '../../core/constants/api_constants.dart';
import '../../core/error/exceptions.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/remote/api_client.dart';

class AuthRepositoryImpl implements AuthRepository {
  final ApiClient apiClient;
  final SharedPreferences prefs; // Temporary replacement for FlutterSecureStorage

  AuthRepositoryImpl(this.apiClient, this.prefs);

  @override
  Future<User> login(String phone, String password) async {
    try {
      final response = await apiClient.postData(
        ApiConstants.loginUri,
        {'phone': phone, 'password': password},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = response.data;

        if (responseData['token'] != null) {
          await saveToken(responseData['token']);

          // Save credentials securely
          await _saveUserCredentials(phone, password);

          // Update API client headers with the new token
          apiClient.updateHeader(responseData['token']);

          // Return user data
          return User(
            id: responseData['id'] ?? 0,
            name: responseData['name'] ?? '',
            email: responseData['email'] ?? '',
            phone: responseData['phone'] ?? '',
            image: responseData['image'],
            type: responseData['type'],
            token: responseData['token'],
          );
        } else {
          throw AuthenticationException(message: 'Token not found in response');
        }
      } else {
        throw ServerException(
          statusCode: response.statusCode ?? 0,
          message: 'Login failed with status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is ServerException || e is AuthenticationException) {
        rethrow;
      }
      throw AuthenticationException(message: 'Login failed: $e');
    }
  }

  @override
  Future<bool> logout() async {
    try {
      await clearToken();
      await _clearUserCredentials();
      return true;
    } catch (e) {
      throw AuthenticationException(message: 'Logout failed: $e');
    }
  }

  @override
  Future<bool> saveToken(String token) async {
    try {
      await prefs.setString('token', token);
      return true;
    } catch (e) {
      throw CacheException(message: 'Failed to save token: $e');
    }
  }

  @override
  String? getToken() {
    try {
      return prefs.getString('token');
    } catch (e) {
      throw CacheException(message: 'Failed to get token: $e');
    }
  }

  @override
  Future<bool> clearToken() async {
    try {
      await prefs.remove('token');
      return true;
    } catch (e) {
      throw CacheException(message: 'Failed to clear token: $e');
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final token = prefs.getString('token');
      return token != null && token.isNotEmpty;
    } catch (e) {
      throw CacheException(message: 'Failed to check login status: $e');
    }
  }

  Future<void> _saveUserCredentials(String phone, String password) async {
    try {
      await prefs.setString('user_phone', phone);
      await prefs.setString('user_password', password);
    } catch (e) {
      throw CacheException(message: 'Failed to save user credentials: $e');
    }
  }

  Future<void> _clearUserCredentials() async {
    try {
      await prefs.remove('user_phone');
      await prefs.remove('user_password');
    } catch (e) {
      throw CacheException(message: 'Failed to clear user credentials: $e');
    }
  }
}
