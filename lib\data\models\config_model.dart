class ConfigModel {
  String? companyName;
  String? companyLogo;
  String? companyAddress;
  String? companyPhone;
  String? companyEmail;
  BaseUrls? baseUrls;
  String? currencySymbol;
  String? currencyPosition;
  bool? twoFactor;
  bool? phoneVerification;
  String? country;
  String? languageCode;
  String? termsAndConditions;
  String? privacyPolicy;
  String? aboutUs;
  int? themeIndex;
  List<String>? activePaymentMethodList;
  int? otpResendTime;
  SystemFeature? systemFeature;

  ConfigModel({
    this.companyName,
    this.companyLogo,
    this.companyAddress,
    this.companyPhone,
    this.companyEmail,
    this.baseUrls,
    this.currencySymbol,
    this.currencyPosition,
    this.twoFactor,
    this.phoneVerification,
    this.country,
    this.languageCode,
    this.termsAndConditions,
    this.privacyPolicy,
    this.aboutUs,
    this.themeIndex,
    this.activePaymentMethodList,
    this.otpResendTime,
    this.systemFeature,
  });

  ConfigModel.fromJson(Map<String, dynamic> json) {
    companyName = json['company_name'];
    companyLogo = json['company_logo'];
    companyAddress = json['company_address'];
    companyPhone = json['company_phone'];
    companyEmail = json['company_email'];
    baseUrls = json['base_urls'] != null ? BaseUrls.fromJson(json['base_urls']) : null;
    currencySymbol = json['currency_symbol'];
    currencyPosition = json['currency_position'];
    twoFactor = json['two_factor'];
    phoneVerification = json['phone_verification'];
    country = json['country'];
    languageCode = json['language_code'];
    termsAndConditions = json['terms_and_conditions'];
    privacyPolicy = json['privacy_policy'];
    aboutUs = json['about_us'];
    themeIndex = json['theme_index'];
    activePaymentMethodList = json['active_payment_method_list']?.cast<String>();
    otpResendTime = json['otp_resend_time'];
    systemFeature = json['system_feature'] != null ? SystemFeature.fromJson(json['system_feature']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['company_name'] = companyName;
    data['company_logo'] = companyLogo;
    data['company_address'] = companyAddress;
    data['company_phone'] = companyPhone;
    data['company_email'] = companyEmail;
    if (baseUrls != null) {
      data['base_urls'] = baseUrls!.toJson();
    }
    data['currency_symbol'] = currencySymbol;
    data['currency_position'] = currencyPosition;
    data['two_factor'] = twoFactor;
    data['phone_verification'] = phoneVerification;
    data['country'] = country;
    data['language_code'] = languageCode;
    data['terms_and_conditions'] = termsAndConditions;
    data['privacy_policy'] = privacyPolicy;
    data['about_us'] = aboutUs;
    data['theme_index'] = themeIndex;
    data['active_payment_method_list'] = activePaymentMethodList;
    data['otp_resend_time'] = otpResendTime;
    if (systemFeature != null) {
      data['system_feature'] = systemFeature!.toJson();
    }
    return data;
  }
}

class BaseUrls {
  String? customerImageUrl;
  String? agentImageUrl;
  String? linkedWebsiteImageUrl;
  String? purposeImageUrl;
  String? notificationImageUrl;
  String? companyImageUrl;
  String? bannerImageUrl;

  BaseUrls({
    this.customerImageUrl,
    this.agentImageUrl,
    this.linkedWebsiteImageUrl,
    this.purposeImageUrl,
    this.notificationImageUrl,
    this.companyImageUrl,
    this.bannerImageUrl,
  });

  BaseUrls.fromJson(Map<String, dynamic> json) {
    customerImageUrl = json['customer_image_url'];
    agentImageUrl = json['agent_image_url'];
    linkedWebsiteImageUrl = json['linked_website_image_url'];
    purposeImageUrl = json['purpose_image_url'];
    notificationImageUrl = json['notification_image_url'];
    companyImageUrl = json['company_image_url'];
    bannerImageUrl = json['banner_image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['customer_image_url'] = customerImageUrl;
    data['agent_image_url'] = agentImageUrl;
    data['linked_website_image_url'] = linkedWebsiteImageUrl;
    data['purpose_image_url'] = purposeImageUrl;
    data['notification_image_url'] = notificationImageUrl;
    data['company_image_url'] = companyImageUrl;
    data['banner_image_url'] = bannerImageUrl;
    return data;
  }
}

class SystemFeature {
  bool? addMoneyStatus;
  bool? sendMoneyStatus;
  bool? cashOutStatus;
  bool? requestMoneyStatus;
  bool? qrCodeStatus;
  bool? kycStatus;

  SystemFeature({
    this.addMoneyStatus,
    this.sendMoneyStatus,
    this.cashOutStatus,
    this.requestMoneyStatus,
    this.qrCodeStatus,
    this.kycStatus,
  });

  SystemFeature.fromJson(Map<String, dynamic> json) {
    addMoneyStatus = json['add_money_status'];
    sendMoneyStatus = json['send_money_status'];
    cashOutStatus = json['cash_out_status'];
    requestMoneyStatus = json['request_money_status'];
    qrCodeStatus = json['qr_code_status'];
    kycStatus = json['kyc_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['add_money_status'] = addMoneyStatus;
    data['send_money_status'] = sendMoneyStatus;
    data['cash_out_status'] = cashOutStatus;
    data['request_money_status'] = requestMoneyStatus;
    data['qr_code_status'] = qrCodeStatus;
    data['kyc_status'] = kycStatus;
    return data;
  }
}
