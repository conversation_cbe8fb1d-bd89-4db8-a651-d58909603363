import 'package:equatable/equatable.dart';

class Config extends Equatable {
  final String? companyName;
  final String? companyLogo;
  final String? companyAddress;
  final String? companyPhone;
  final String? companyEmail;
  final String? currencySymbol;
  final String? currencyPosition;
  final bool? twoFactor;
  final bool? phoneVerification;
  final String? country;
  final String? languageCode;
  final String? termsAndConditions;
  final String? privacyPolicy;
  final String? aboutUs;
  final int? themeIndex;
  final List<String>? activePaymentMethodList;
  final int? otpResendTime;

  const Config({
    this.companyName,
    this.companyLogo,
    this.companyAddress,
    this.companyPhone,
    this.companyEmail,
    this.currencySymbol,
    this.currencyPosition,
    this.twoFactor,
    this.phoneVerification,
    this.country,
    this.languageCode,
    this.termsAndConditions,
    this.privacyPolicy,
    this.aboutUs,
    this.themeIndex,
    this.activePaymentMethodList,
    this.otpResendTime,
  });

  @override
  List<Object?> get props => [
        companyName,
        companyLogo,
        companyAddress,
        companyPhone,
        companyEmail,
        currencySymbol,
        currencyPosition,
        twoFactor,
        phoneVerification,
        country,
        languageCode,
        termsAndConditions,
        privacyPolicy,
        aboutUs,
        themeIndex,
        activePaymentMethodList,
        otpResendTime,
      ];
}
