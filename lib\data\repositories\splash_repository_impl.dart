import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/api_constants.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/config.dart';
import '../../domain/repositories/splash_repository.dart';
import '../datasources/remote/api_client.dart';
import '../models/config_model.dart';

class SplashRepositoryImpl implements SplashRepository {
  final ApiClient apiClient;
  final SharedPreferences sharedPreferences;

  SplashRepositoryImpl(this.apiClient, this.sharedPreferences);

  @override
  Future<Config> getConfigData() async {
    try {
      final response = await apiClient.getData(ApiConstants.configUri);
      
      if (response.statusCode == 200) {
        final ConfigModel configModel = ConfigModel.fromJson(response.data);
        
        return Config(
          companyName: configModel.companyName,
          companyLogo: configModel.companyLogo,
          companyAddress: configModel.companyAddress,
          companyPhone: configModel.companyPhone,
          companyEmail: configModel.companyEmail,
          currencySymbol: configModel.currencySymbol,
          currencyPosition: configModel.currencyPosition,
          twoFactor: configModel.twoFactor,
          phoneVerification: configModel.phoneVerification,
          country: configModel.country,
          languageCode: configModel.languageCode,
          termsAndConditions: configModel.termsAndConditions,
          privacyPolicy: configModel.privacyPolicy,
          aboutUs: configModel.aboutUs,
          themeIndex: configModel.themeIndex,
          activePaymentMethodList: configModel.activePaymentMethodList,
          otpResendTime: configModel.otpResendTime,
        );
      } else {
        throw Exception('Failed to load config data');
      }
    } catch (e) {
      throw Exception('Failed to load config data: $e');
    }
  }

  @override
  Future<bool> initSharedData() async {
    if (!sharedPreferences.containsKey(AppConstants.theme)) {
      await sharedPreferences.setBool(AppConstants.theme, false);
    }
    
    if (!sharedPreferences.containsKey(AppConstants.customerCountryCode)) {
      await sharedPreferences.setString(
        AppConstants.customerCountryCode,
        'US',
      );
    }
    
    if (!sharedPreferences.containsKey(AppConstants.languageCode)) {
      await sharedPreferences.setString(
        AppConstants.languageCode,
        'en',
      );
    }
    
    return true;
  }

  @override
  Future<bool> removeSharedData() async {
    return await sharedPreferences.clear();
  }
}
